import {Connection} from "mongoose";
import { DefaultSession } from "next-auth";

declare module "next-auth" {
    interface Session {
        user: {
            id: string;
            email: string;
            name?: string;
        } & DefaultSession["user"];
    }
}

declare global{
    var mongoose:{
        conn: Connection | null;
        promise: Promise<Connection> | null;
    }
}

export {}