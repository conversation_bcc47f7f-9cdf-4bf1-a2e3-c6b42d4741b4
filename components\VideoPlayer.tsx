"use client";

import { IVideo } from "@/models/Video";
import { useEffect, useRef } from "react";

interface VideoPlayerProps {
    video: IVideo;
    onClose: () => void;
}

export default function VideoPlayer({ video, onClose }: VideoPlayerProps) {
    const modalRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === "Escape") {
                onClose();
            }
        };

        const handleClickOutside = (e: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
                onClose();
            }
        };

        document.addEventListener("keydown", handleEscape);
        document.addEventListener("mousedown", handleClickOutside);
        document.body.style.overflow = "hidden";

        return () => {
            document.removeEventListener("keydown", handleEscape);
            document.removeEventListener("mousedown", handleClickOutside);
            document.body.style.overflow = "unset";
        };
    }, [onClose]);

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
            <div
                ref={modalRef}
                className="relative w-full max-w-4xl mx-4 bg-white rounded-lg overflow-hidden shadow-2xl"
            >
                {/* Header */}
                <div className="flex justify-between items-center p-4 border-b">
                    <div>
                        <h2 className="text-xl font-semibold text-gray-900">
                            {video.title}
                        </h2>
                        <p className="text-gray-600 text-sm mt-1">
                            {video.description}
                        </p>
                    </div>
                    <button
                        onClick={onClose}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                            />
                        </svg>
                    </button>
                </div>

                {/* Video Player */}
                <div className="relative aspect-video bg-black">
                    <video
                        className="w-full h-full"
                        controls={video.controls}
                        autoPlay
                        preload="metadata"
                        poster={video.thumbnailUrl}
                    >
                        <source src={video.videoUrl} type="video/mp4" />
                        <source src={video.videoUrl} type="video/webm" />
                        <source src={video.videoUrl} type="video/ogg" />
                        Your browser does not support the video tag.
                    </video>
                </div>

                {/* Video Info */}
                <div className="p-4 bg-gray-50">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span className="font-medium text-gray-700">Quality:</span>
                            <span className="ml-2 text-gray-600">
                                {video.transformation?.quality || 100}%
                            </span>
                        </div>
                        <div>
                            <span className="font-medium text-gray-700">Dimensions:</span>
                            <span className="ml-2 text-gray-600">
                                {video.transformation?.width || 1280} x {video.transformation?.height || 720}
                            </span>
                        </div>
                        <div>
                            <span className="font-medium text-gray-700">Controls:</span>
                            <span className="ml-2 text-gray-600">
                                {video.controls ? "Enabled" : "Disabled"}
                            </span>
                        </div>
                    </div>

                    <div className="mt-4 flex space-x-2">
                        <a
                            href={video.videoUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                />
                            </svg>
                            Open in New Tab
                        </a>
                        
                        <button
                            onClick={() => {
                                navigator.clipboard.writeText(video.videoUrl);
                                alert("Video URL copied to clipboard!");
                            }}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                                />
                            </svg>
                            Copy URL
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}
