# AI Video Uploading Platform

A modern, full-stack video uploading and management platform built with Next.js 15, featuring AI-enhanced video processing, user authentication, and a beautiful dashboard interface.

## ✨ Features

- 🎥 **Video Upload & Management** - Upload videos with drag-and-drop interface
- 🔐 **User Authentication** - Secure login/register with NextAuth.js
- 📱 **Responsive Dashboard** - Beautiful, mobile-friendly video management interface
- 🎬 **Video Player** - Custom video player with controls and metadata
- 🖼️ **Thumbnail Generation** - Automatic thumbnail creation via ImageKit
- 🗂️ **Gallery Views** - Grid and list view options for video organization
- ⚡ **Real-time Progress** - Upload progress tracking with visual feedback
- 🎨 **Modern UI** - Clean, professional interface with Tailwind CSS
- 🔍 **Video Search** - Easy video discovery and management
- 📊 **Video Analytics** - Track video performance and metadata

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, MongoDB with Mongoose
- **Authentication**: NextAuth.js with credentials provider
- **File Upload**: ImageKit integration for video processing
- **Styling**: Tailwind CSS with custom components
- **Database**: MongoDB for user and video data storage

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- MongoDB database (local or MongoDB Atlas)
- ImageKit account for video processing

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd ai_video_uploading
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:

```env
# Database
MONGODB_URL=mongodb://localhost:27017/ai_video_uploading

# NextAuth Configuration
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# ImageKit Configuration
IMAGEKIT_PRIVATE_KEY=your-imagekit-private-key
NEXT_PUBLIC_PUBLIC_KEY=your-imagekit-public-key
NEXT_PUBLIC_URL_ENDPOINT=https://ik.imagekit.io/your-imagekit-id
```

4. **Run the development server**
```bash
npm run dev
```

5. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
├── app/                    # Next.js 15 App Router
│   ├── api/               # API routes
│   │   ├── auth/          # Authentication endpoints
│   │   └── video/         # Video management endpoints
│   ├── dashboard/         # Dashboard page
│   ├── login/            # Login page
│   ├── register/         # Registration page
│   └── layout.tsx        # Root layout
├── components/            # Reusable React components
│   ├── FileUpload.tsx    # Video upload component
│   ├── VideoGallery.tsx  # Video gallery display
│   ├── VideoCard.tsx     # Individual video card
│   ├── VideoPlayer.tsx   # Custom video player
│   └── Providers.tsx     # Context providers
├── lib/                  # Utility libraries
│   ├── api-client.ts     # API client functions
│   ├── auth.ts           # NextAuth configuration
│   └── db.ts             # Database connection
├── models/               # MongoDB schemas
│   ├── User.ts           # User model
│   └── Video.ts          # Video model
└── types.d.ts           # TypeScript type definitions
```

## 🎯 Usage

### For Users

1. **Register/Login** - Create an account or sign in
2. **Upload Videos** - Use the dashboard to upload video files
3. **Manage Videos** - View, play, and organize your video library
4. **Share Videos** - Copy video URLs to share with others

### For Developers

The platform provides a clean API structure for extending functionality:

- **Authentication**: Built-in user management with NextAuth.js
- **File Upload**: ImageKit integration for video processing
- **Database**: MongoDB with Mongoose for data persistence
- **API Routes**: RESTful endpoints for video and user management

## 🔧 Configuration

### ImageKit Setup

1. Create an account at [ImageKit.io](https://imagekit.io)
2. Get your URL endpoint, public key, and private key
3. Add them to your `.env.local` file

### MongoDB Setup

**Local MongoDB:**
```bash
# Install MongoDB locally or use Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

**MongoDB Atlas:**
1. Create a cluster at [MongoDB Atlas](https://cloud.mongodb.com)
2. Get your connection string
3. Add it to your `.env.local` file

## 📚 Learn More

To learn more about the technologies used:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
