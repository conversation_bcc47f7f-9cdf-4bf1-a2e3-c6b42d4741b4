import { authOptions } from "@/lib/auth"
import { connectToDatabase } from "@/lib/db"
import Video, { IVideo } from "@/models/Video"
import { getServerSession } from "next-auth"
import { NextRequest, NextResponse } from "next/server"

export async function GET() {
    try {
        const session = await getServerSession(authOptions)
        if (!session || !session.user?.id) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401}
            )
        }

        await connectToDatabase()
        const videos = await Video.find({ userId: session.user.id }).sort({ createdAt: -1 }).lean()

        return NextResponse.json(videos)
    }
    catch (error) {
        console.error("Failed to fetch videos:", error);
        return NextResponse.json(
            { error: "failed to fetch videos" },
            { status: 500 }
        )
    }
}

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions)
        if (!session || !session.user?.id) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401}
            )
        }
        await connectToDatabase()
        const body: IVideo = await request.json()
        if(!body.title || !body.description || !body.videoUrl || !body.thumbnailUrl){
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 500 }
            );
        }
        const videoData = {
            ...body,
            controls: body?.controls ?? true,
            transformation:{
                height: body.transformation?.height ?? 720,
                width: body.transformation?.width ?? 1280,
                quality: body.transformation?.quality ?? 80,
            },
            title: body.title,
            description: body.description,
            videoUrl: body.videoUrl,
            thumbnailUrl: body.thumbnailUrl,
            userId: session.user.id,
        }
        const newVideo = await Video.create(videoData)
        return NextResponse.json(newVideo)
    }
    catch (error) {
        console.error("Failed to create video:", error);
        return NextResponse.json(
            { error: "failed to create video" },
            { status: 500 }
        )
    }
}