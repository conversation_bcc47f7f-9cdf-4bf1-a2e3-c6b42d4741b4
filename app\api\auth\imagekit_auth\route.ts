import { getUploadAuthParams } from "@imagekit/next/server"

export async function GET() {
    try{
        // Validate environment variables
        if (!process.env.IMAGEKIT_PRIVATE_KEY || !process.env.NEXT_PUBLIC_PUBLIC_KEY) {
            throw new Error("ImageKit environment variables are not configured");
        }

        const authParams = getUploadAuthParams({
            privateKey: process.env.IMAGEKIT_PRIVATE_KEY,
            publicKey: process.env.NEXT_PUBLIC_PUBLIC_KEY,
        });

        return Response.json({
            ...authParams,
            publicKey: process.env.NEXT_PUBLIC_PUBLIC_KEY
        })
    }catch(error){
        console.error("ImageKit authentication error:", error);
        return Response.json(
            { error: error instanceof Error ? error.message : "Authentication for ImageKit Failed" },
            {status: 500}
        )
    }
}