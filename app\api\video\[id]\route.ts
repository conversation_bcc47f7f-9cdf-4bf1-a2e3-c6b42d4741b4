import { authOptions } from "@/lib/auth";
import { connectToDatabase } from "@/lib/db";
import Video from "@/models/Video";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

export async function DELETE(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    const { id } = await params;
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        await connectToDatabase();
        
        const video = await Video.findById(id);
        if (!video) {
            return NextResponse.json(
                { error: "Video not found" },
                { status: 404 }
            );
        }

        // Check if user owns this video
        if (video.userId !== session.user.id) {
            return NextResponse.json(
                { error: "Forbidden: You can only delete your own videos" },
                { status: 403 }
            );
        }

        await Video.findByIdAndDelete(id);
        
        return NextResponse.json(
            { message: "Video deleted successfully" },
            { status: 200 }
        );
    } catch (error) {
        console.error("Failed to delete video:", error);
        return NextResponse.json(
            { error: "Failed to delete video" },
            { status: 500 }
        );
    }
}

export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    const { id } = await params;
    try {
        await connectToDatabase();
        
        const video = await Video.findById(id);
        if (!video) {
            return NextResponse.json(
                { error: "Video not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(video);
    } catch (error) {
        console.error("Failed to fetch video:", error);
        return NextResponse.json(
            { error: "Failed to fetch video" },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    const { id } = await params;
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        await connectToDatabase();
        
        const body = await request.json();
        const { title, description, controls, transformation } = body;

        const video = await Video.findById(id);
        if (!video) {
            return NextResponse.json(
                { error: "Video not found" },
                { status: 404 }
            );
        }

        // Check if user owns this video
        if (video.userId !== session.user.id) {
            return NextResponse.json(
                { error: "Forbidden: You can only update your own videos" },
                { status: 403 }
            );
        }

        const updatedVideo = await Video.findByIdAndUpdate(
            id,
            {
                title,
                description,
                controls,
                transformation,
            },
            { new: true }
        );

        return NextResponse.json(updatedVideo);
    } catch (error) {
        console.error("Failed to update video:", error);
        return NextResponse.json(
            { error: "Failed to update video" },
            { status: 500 }
        );
    }
}
