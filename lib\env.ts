// Environment variables validation
export const env = {
  // Database
  MONGODB_URL: process.env.MONGODB_URL || '',
  
  // NextAuth
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || '',
  NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  
  // ImageKit
  IMAGEKIT_PRIVATE_KEY: process.env.IMAGEKIT_PRIVATE_KEY || '',
  NEXT_PUBLIC_PUBLIC_KEY: process.env.NEXT_PUBLIC_PUBLIC_KEY || '',
  NEXT_PUBLIC_URL_ENDPOINT: process.env.NEXT_PUBLIC_URL_ENDPOINT || '',
};

// Validation function
export function validateEnv() {
  const requiredVars = [
    'MONGODB_URL',
    'NEXTAUTH_SECRET',
    'IMAGEKIT_PRIVATE_KEY',
    'NEXT_PUBLIC_PUBLIC_KEY',
    'NEXT_PUBLIC_URL_ENDPOINT',
  ];

  const missingVars = requiredVars.filter(varName => !env[varName as keyof typeof env]);

  if (missingVars.length > 0) {
    console.warn(
      `Warning: Missing environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env.local file and ensure all required variables are set.'
    );
  }

  return missingVars.length === 0;
}

// Check environment variables on import (only in development)
if (process.env.NODE_ENV === 'development') {
  validateEnv();
}
