import { IVideo } from "@/models/Video";

export type VideoFormData = Omit<IVideo, "_id" | "createdAt" | "updatedAt">;

type FetchOptions = {
    method?: "GET" | "POST" | "PUT" | "DELETE";
    body?: unknown;
    headers?: Record<string, string>
}

export class ApiClient {
    private async fetch<T>(
        endpoint: string,
        options: FetchOptions = {}
    ): Promise<T> {
        const { method = "GET", body, headers = {} } = options;
        const defaultHeaders = {
            "Content-Type": "application/json",
            ...headers
        };
        const response = await fetch(`/api${endpoint}`, {
            method,
            body: body ? JSON.stringify(body) : undefined,
            headers: defaultHeaders,
        });
        if (!response.ok) {
            throw new Error(await response.text())
        }
        return response.json() as Promise<T>;
    }
    async getVideos() {
        return this.fetch<IVideo[]>("/video")
    }
    async createVideo(videoData: VideoFormData) {
        return this.fetch<IVideo>("/video", {
            method: "POST",
            body: videoData,
        })
    }

    async deleteVideo(videoId: string) {
        return this.fetch(`/video/${videoId}`, {
            method: "DELETE",
        })
    }

    async updateVideo(videoId: string, videoData: Partial<VideoFormData>) {
        return this.fetch<IVideo>(`/video/${videoId}`, {
            method: "PUT",
            body: videoData,
        })
    }

    async getVideo(videoId: string) {
        return this.fetch<IVideo>(`/video/${videoId}`)
    }
}
export const apiClient = new ApiClient();