import mongoose,{Schema,model,models} from "mongoose";

export const VIDEO_DIMENSIONS ={
    width:1280,
    height:720,
} as const;


export interface IVideo{
    _id?:mongoose.Types.ObjectId
    title:string;
    description:string;
    videoUrl:string;
    thumbnailUrl:string;
    controls?:boolean;
    transformation?:{
        height:number;
        width:number;
        quality?:number;
    };
    userId?: string;
    createdAt?: string;
    updatedAt?: string;
}

const VideoSchema = new Schema<IVideo>(
    {
        title:{type:String,required:true},
        description:{type:String,required:true},
        videoUrl:{type:String,required:true},
        thumbnailUrl:{type:String,required:true},
        controls:{type:Boolean,default :true},
        transformation:{
            height:{type:Number,default:VIDEO_DIMENSIONS.height},
            width:{type:Number,default:VIDEO_DIMENSIONS.width},
            quality:{type:Number,min:1,max:100,default:100},
        },
        userId:{type:String,required:false},
    },
    {timestamps:true}
);

const Video = models?.Video || model<IVideo>("Video",VideoSchema);
export default Video;