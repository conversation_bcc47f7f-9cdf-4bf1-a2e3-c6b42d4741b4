"use client"

import { ImageKitProvider } from "@imagekit/next";
import { SessionProvider } from "next-auth/react";

const urlEndpoint = process.env.NEXT_PUBLIC_URL_ENDPOINT;

if (!urlEndpoint) {
    console.warn("NEXT_PUBLIC_URL_ENDPOINT is not defined. ImageKit features may not work properly.");
}

export default function Providers({ children }: { children: React.ReactNode }) {
    return (
        <SessionProvider refetchInterval={5 * 60}>
            {urlEndpoint ? (
                <ImageKitProvider urlEndpoint={urlEndpoint}>{children}</ImageKitProvider>
            ) : (
                children
            )}
        </SessionProvider>
    );
}
 

