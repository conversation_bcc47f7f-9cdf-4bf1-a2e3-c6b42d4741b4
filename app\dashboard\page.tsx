"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import FileUpload from "@/components/FileUpload";
import VideoGallery from "@/components/VideoGallery";
import { apiClient } from "@/lib/api-client";
import { IVideo } from "@/models/Video";

export default function Dashboard() {
    const { data: session, status } = useSession();
    const router = useRouter();
    const [videos, setVideos] = useState<IVideo[]>([]);
    const [loading, setLoading] = useState(true);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [showUpload, setShowUpload] = useState(false);

    useEffect(() => {
        if (status === "loading") return;
        if (!session) {
            router.push("/login");
            return;
        }
        fetchVideos();
    }, [session, status, router]);

    const fetchVideos = async () => {
        try {
            setLoading(true);
            const fetchedVideos = await apiClient.getVideos();
            setVideos(fetchedVideos);
        } catch (error) {
            console.error("Failed to fetch videos:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleUploadSuccess = async (uploadResult: { name?: string; url?: string; thumbnailUrl?: string }) => {
        try {
            // Create video record in database
            const videoData = {
                title: uploadResult.name || "Untitled Video",
                description: "Uploaded video",
                videoUrl: uploadResult.url || "",
                thumbnailUrl: uploadResult.thumbnailUrl || uploadResult.url || "",
                controls: true,
                transformation: {
                    height: 720,
                    width: 1280,
                    quality: 80
                }
            };

            await apiClient.createVideo(videoData);
            await fetchVideos(); // Refresh the video list
            setShowUpload(false);
            setUploadProgress(0);
        } catch (error) {
            console.error("Failed to save video:", error);
        }
    };

    if (status === "loading") {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (!session) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <header className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-6">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">
                                Video Dashboard
                            </h1>
                            <p className="text-gray-600">
                                Welcome back, {session.user?.email}
                            </p>
                        </div>
                        <div className="flex space-x-4">
                            <button
                                onClick={() => setShowUpload(!showUpload)}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                            >
                                {showUpload ? "Cancel Upload" : "Upload Video"}
                            </button>
                            <button
                                onClick={() => router.push("/api/auth/signout")}
                                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                            >
                                Sign Out
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {showUpload && (
                    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 className="text-xl font-semibold mb-4">Upload New Video</h2>
                        <FileUpload
                            onSuccess={handleUploadSuccess}
                            onProgress={setUploadProgress}
                            fileType="video"
                        />
                        {uploadProgress > 0 && (
                            <div className="mt-4">
                                <div className="bg-gray-200 rounded-full h-2">
                                    <div
                                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${uploadProgress}%` }}
                                    ></div>
                                </div>
                                <p className="text-sm text-gray-600 mt-2">
                                    Upload Progress: {uploadProgress}%
                                </p>
                            </div>
                        )}
                    </div>
                )}

                <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-semibold">Your Videos</h2>
                        <button
                            onClick={fetchVideos}
                            className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                            Refresh
                        </button>
                    </div>

                    {loading ? (
                        <div className="flex justify-center py-12">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        </div>
                    ) : (
                        <VideoGallery videos={videos} onVideoUpdate={fetchVideos} />
                    )}
                </div>
            </main>
        </div>
    );
}
