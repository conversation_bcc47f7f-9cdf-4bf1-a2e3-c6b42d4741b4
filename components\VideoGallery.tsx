"use client";

import { IVideo } from "@/models/Video";
import { useState } from "react";
import VideoPlayer from "./VideoPlayer";
import VideoCard from "./VideoCard";

interface VideoGalleryProps {
    videos: IVideo[];
    onVideoUpdate: () => void;
}

export default function VideoGallery({ videos, onVideoUpdate }: VideoGalleryProps) {
    const [selectedVideo, setSelectedVideo] = useState<IVideo | null>(null);
    const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

    if (videos.length === 0) {
        return (
            <div className="text-center py-12">
                <div className="mx-auto h-24 w-24 text-gray-400">
                    <svg
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                        />
                    </svg>
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">No videos yet</h3>
                <p className="mt-2 text-gray-500">
                    Get started by uploading your first video.
                </p>
            </div>
        );
    }

    return (
        <div>
            {/* View Mode Toggle */}
            <div className="flex justify-between items-center mb-6">
                <p className="text-gray-600">
                    {videos.length} video{videos.length !== 1 ? "s" : ""} found
                </p>
                <div className="flex space-x-2">
                    <button
                        onClick={() => setViewMode("grid")}
                        className={`p-2 rounded ${
                            viewMode === "grid"
                                ? "bg-blue-100 text-blue-600"
                                : "text-gray-400 hover:text-gray-600"
                        }`}
                    >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                    </button>
                    <button
                        onClick={() => setViewMode("list")}
                        className={`p-2 rounded ${
                            viewMode === "list"
                                ? "bg-blue-100 text-blue-600"
                                : "text-gray-400 hover:text-gray-600"
                        }`}
                    >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                fillRule="evenodd"
                                d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                clipRule="evenodd"
                            />
                        </svg>
                    </button>
                </div>
            </div>

            {/* Video Grid/List */}
            <div
                className={
                    viewMode === "grid"
                        ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                        : "space-y-4"
                }
            >
                {videos.map((video) => (
                    <VideoCard
                        key={video._id?.toString()}
                        video={video}
                        viewMode={viewMode}
                        onPlay={() => setSelectedVideo(video)}
                        onUpdate={onVideoUpdate}
                    />
                ))}
            </div>

            {/* Video Player Modal */}
            {selectedVideo && (
                <VideoPlayer
                    video={selectedVideo}
                    onClose={() => setSelectedVideo(null)}
                />
            )}
        </div>
    );
}
