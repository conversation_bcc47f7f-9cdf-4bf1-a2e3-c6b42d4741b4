"use client" // This component must be a client component

import { upload } from "@imagekit/next";
import { useState } from "react";

interface UploadResult {
    name?: string;
    url?: string;
    thumbnailUrl?: string;
    [key: string]: unknown;
}

interface FileUploadProps {
    onSuccess: (res: UploadResult) => void;
    onProgress?: (progress: number) => void;
    fileType?: "image" | "video";
}


const FileUpload = ({
    onSuccess,
    onProgress,
    fileType,
}: FileUploadProps) => {


    const [uploading, setUploading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Optional Validation 
    const validateFile = (file: File) => {
        if (fileType === "video") {
            if (!file.type.startsWith("video/")) {
                setError("Invalid file type. Please upload a video.");
                return false;
            }
        }
        if (file.size > 100 * 1024 * 1024) {
            setError("File size exceeds the limit. Please upload a file smaller than 100MB.");
            return false;
        }
        return true;
    }


    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (!file || !validateFile(file)) {
            return;
        }
        setUploading(true)
        setError(null)
        try {
            const auth = await fetch("/api/auth/imagekit_auth").then((res) => res.json())
            const response = await upload({
                file,
                fileName: file.name,
                publicKey: auth.publicKey,
                signature: auth.signature,
                expire: auth.expire,
                token: auth.token,
                onProgress: (event) => {
                    if (event.lengthComputable && onProgress) {
                        const percent = (event.loaded / event.total) * 100;
                        onProgress(Math.round(percent));
                    }
                },
            });
            onSuccess(response as unknown as UploadResult);
        }
        catch (error) {
            console.error("upload failed", error);
            setError(error instanceof Error ? error.message : "Upload failed. Please try again.");
        }
        finally {
            setUploading(false);
        }
    }
    return (
        <div className="w-full">
            <div className="flex items-center justify-center w-full">
                <label
                    htmlFor="file-upload"
                    className="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <svg
                            className="w-8 h-8 mb-4 text-gray-500"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 20 16"
                        >
                            <path
                                stroke="currentColor"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                            />
                        </svg>
                        <p className="mb-2 text-sm text-gray-500">
                            <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">
                            {fileType === "video" ? "MP4, WebM, OGG (MAX. 100MB)" : "PNG, JPG, GIF (MAX. 100MB)"}
                        </p>
                    </div>
                    <input
                        id="file-upload"
                        type="file"
                        className="hidden"
                        accept={fileType === "video" ? "video/*" : "image/*"}
                        onChange={handleFileChange}
                        disabled={uploading}
                    />
                </label>
            </div>

            {uploading && (
                <div className="mt-4 text-center">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-blue-500 bg-blue-100">
                        <svg
                            className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                        >
                            <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                            ></circle>
                            <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        Uploading...
                    </div>
                </div>
            )}

            {error && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                    <div className="flex">
                        <div className="flex-shrink-0">
                            <svg
                                className="h-5 w-5 text-red-400"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                            >
                                <path
                                    fillRule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                    clipRule="evenodd"
                                />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <p className="text-sm text-red-800">{error}</p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default FileUpload;